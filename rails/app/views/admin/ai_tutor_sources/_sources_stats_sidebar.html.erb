<!-- Sources Stats Sidebar -->
<div class="sources-stats-sidebar">
  <div class="stats-title">Sources</div>

  <%
    # Get optimized stats
    sources_by_type = @ai_tutor_agent.sources_count_by_type
    content_sizes = @ai_tutor_agent.content_size_stats
    total_size = @ai_tutor_agent.total_content_size
    max_size = 33 * 1024 * 1024 # 33 MB in bytes
  %>

  <ul class="stats-list">
    <% if sources_by_type['file'] && sources_by_type['file'] > 0 %>
      <li class="stats-item">
        <div class="stats-label">
          <i class="bi bi-file-earmark"></i>
          Files
        </div>
        <div class="stats-value"><%= number_to_human_size(content_sizes['file'] || 0) %></div>
      </li>
    <% end %>

    <% if sources_by_type['text'] && sources_by_type['text'] > 0 %>
      <li class="stats-item">
        <div class="stats-label">
          <i class="bi bi-file-text"></i>
          Text Files
        </div>
        <div class="stats-value"><%= number_to_human_size(content_sizes['text'] || 0) %></div>
      </li>
    <% end %>

    <% if sources_by_type['web'] && sources_by_type['web'] > 0 %>
      <li class="stats-item">
        <div class="stats-label">
          <i class="bi bi-link-45deg"></i>
          Links
        </div>
        <div class="stats-value"><%= number_to_human_size(content_sizes['web'] || 0) %></div>
      </li>
    <% end %>

    <% if sources_by_type['qa'] && sources_by_type['qa'] > 0 %>
      <li class="stats-item">
        <div class="stats-label">
          <i class="bi bi-question-circle"></i>
          Q&A
        </div>
        <div class="stats-value"><%= number_to_human_size(content_sizes['qa'] || 0) %></div>
      </li>
    <% end %>

    <% if sources_by_type['notion'] && sources_by_type['notion'] > 0 %>
      <li class="stats-item">
        <div class="stats-label">
          <i class="bi bi-journal-text"></i>
          Notion Page
        </div>
        <div class="stats-value"><%= number_to_human_size(content_sizes['notion'] || 0) %></div>
      </li>
    <% end %>
  </ul>

  <div class="total-size">
    <div class="total-size-label">Total size:</div>
    <div class="total-size-value">
      <%= number_to_human_size(total_size) %>
      <div class="total-size-subtext">/ <%= number_to_human_size(max_size) %></div>
    </div>
  </div>

  <!-- Training Status -->
  <div class="training-section">
    <div class="training-status">
      <%
        # Get comprehensive training summary
        training_summary = @ai_tutor_agent.training_summary

        agent_status = training_summary[:agent]
        sources_stats = training_summary[:sources]
        content_stats = training_summary[:content_pieces]
        last_activity = training_summary[:last_activity]
      %>

      <div class="training-stats">
        <!-- Agent Training Status -->
        <div class="training-section-title">
          AI Agent
          <div class="agent-status-badge agent-status-<%= agent_status[:training_status] %>">
            <%= agent_status[:training_status].humanize %>
          </div>
        </div>

        <% if agent_status[:last_trained_at] %>
          <div class="training-stat">
            <span class="training-stat-label">Last Trained:</span>
            <span class="training-stat-value"><%= time_ago_in_words(agent_status[:last_trained_at]) %> ago</span>
          </div>
        <% end %>

        <% if agent_status[:training_error] %>
          <div class="training-stat">
            <span class="training-stat-label">Error:</span>
            <span class="training-stat-value failed"><%= truncate(agent_status[:training_error], length: 50) %></span>
          </div>
        <% end %>

        <!-- Sources Training Status -->
        <div class="training-section-title">Sources Training Status</div>

        <div class="training-stat">
          <span class="training-stat-label">Not Trained:</span>
          <span class="training-stat-value not-trained"><%= sources_stats[:not_trained] %></span>
        </div>
        <% if sources_stats[:training] > 0 %>
          <div class="training-stat">
            <span class="training-stat-label">Training:</span>
            <span class="training-stat-value training"><%= sources_stats[:training] %></span>
          </div>
        <% end %>
        <% if sources_stats[:trained] > 0 %>
          <div class="training-stat">
            <span class="training-stat-label">Trained:</span>
            <span class="training-stat-value trained"><%= sources_stats[:trained] %></span>
          </div>
        <% end %>
        <% if sources_stats[:failed] > 0 %>
          <div class="training-stat">
            <span class="training-stat-label">Failed:</span>
            <span class="training-stat-value failed"><%= sources_stats[:failed] %></span>
          </div>
        <% end %>

        <% if content_stats[:total] > 0 %>
          <div class="training-section-title">Content Pieces</div>
          <div class="training-stat">
            <span class="training-stat-label">Total Pieces:</span>
            <span class="training-stat-value"><%= content_stats[:total] %></span>
          </div>
          <div class="training-stat">
            <span class="training-stat-label">Trained Pieces:</span>
            <span class="training-stat-value trained"><%= content_stats[:trained] %></span>
          </div>
          <% if content_stats[:training] > 0 %>
            <div class="training-stat">
              <span class="training-stat-label">Training Pieces:</span>
              <span class="training-stat-value training"><%= content_stats[:training] %></span>
            </div>
          <% end %>
          <% if content_stats[:failed] > 0 %>
            <div class="training-stat">
              <span class="training-stat-label">Failed Pieces:</span>
              <span class="training-stat-value failed"><%= content_stats[:failed] %></span>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>

    <%
      # Check if any training is in progress
      training_in_progress = agent_status[:training_status] == 'training' ||
                            sources_stats[:training] > 0 ||
                            content_stats[:training] > 0

      # Check if there are sources that need training
      has_untrained_content = sources_stats[:not_trained] > 0 ||
                             content_stats[:not_trained] > 0

      # Check if all sources are trained
      all_trained = sources_stats[:total] > 0 &&
                   sources_stats[:not_trained] == 0 &&
                   sources_stats[:training] == 0 &&
                   sources_stats[:failed] == 0
    %>

    <% if training_in_progress %>
      <button class="btn-train-ai training" disabled>
        <i class="bi bi-arrow-clockwise spin"></i>
        Training in Progress...
        <% if sources_stats[:training] > 0 %>
          <span class="train-count"><%= sources_stats[:training] %></span>
        <% end %>
      </button>
    <% elsif has_untrained_content %>
      <button class="btn-train-ai" onclick="trainAiAgent()">
        <i class="bi bi-robot"></i>
        Train AI Agent
        <% if sources_stats[:not_trained] > 0 %>
          <span class="train-count"><%= sources_stats[:not_trained] %></span>
        <% end %>
      </button>
    <% elsif agent_status[:training_status] == 'failed' %>
      <button class="btn-train-ai failed" onclick="trainAiAgent()">
        <i class="bi bi-exclamation-triangle"></i>
        Retry Training
      </button>
    <% elsif all_trained %>
      <button class="btn-train-ai trained" disabled>
        <i class="bi bi-check-circle"></i>
        All Sources Trained
      </button>
    <% else %>
      <button class="btn-train-ai" disabled>
        <i class="bi bi-plus-circle"></i>
        Add Sources to Train
      </button>
    <% end %>
  </div>
</div>
