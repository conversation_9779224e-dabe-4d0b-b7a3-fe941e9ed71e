<div class="files-page">
  <div class="main-header">
    <h1 class="main-title">Files</h1>
    <p class="main-subtitle">Upload documents to train your AI. Extract text from PDFs, DOCX, and TXT files.</p>
  </div>  
  <!-- Add Files Section -->
  <div class="add-files-section">
    <div class="section-header">
      <h2 class="section-title">
        Add files
        <button class="collapse-btn" type="button" data-bs-toggle="collapse" data-bs-target="#addFilesCollapse">
          <i class="bi bi-chevron-up"></i>
        </button>
      </h2>
    </div>

    <div class="collapse show" id="addFilesCollapse">
      <div class="pdf-warning">
        <i class="bi bi-info-circle"></i>
        If you are uploading a PDF, make sure you can select/highlight the text.
      </div>

      <%= form_with url: admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent),
                    method: :post, multipart: true, local: true,
                    class: "file-upload-form", id: "file-upload-form" do |form| %>
        <%= form.hidden_field 'ai_tutor_source[source_type]', value: 'file' %>

        <div class="upload-area" id="file-upload-area">
          <div class="upload-content">
            <div class="upload-icon">
              <i class="bi bi-upload"></i>
            </div>
            <div class="upload-text">
              <p class="upload-main">Drag & drop files here, or click to select files</p>
              <p class="upload-sub">Supported file types: pdf, doc, docx, txt</p>
            </div>
          </div>
          <%= form.file_field 'ai_tutor_source[file]',
                             id: "file-input",
                             accept: ".pdf,.doc,.docx,.txt,.csv,.xls,.xlsx,.ppt,.pptx",
                             multiple: false,
                             style: "display: none;" %>
        </div>

        <!-- Hidden fields for auto-generated values -->
        <%= form.hidden_field 'ai_tutor_source[title]', id: "hidden-title" %>
        <%= form.hidden_field 'ai_tutor_source[description]', value: "Uploaded file" %>
      <% end %>
    </div>
  </div>

  <!-- File Sources Section -->
  <div class="file-sources-section">
    <div class="section-header">
      <h2 class="section-title">File sources</h2>
    </div>

    <div class="file-sources-list">
      <% file_sources = @ai_tutor_agent.ai_tutor_sources.where(source_type: 'file').includes(:ai_tutor_file_source).order(created_at: :desc) %>

      <% if file_sources.any? %>
        <% file_sources.each do |source| %>
          <div class="file-source-item">
            <div class="file-content">
              <div class="file-main-info">
                <div class="file-name">
                  <%= source.ai_tutor_file_source&.filename || source.title %>
                </div>
                <div class="file-size">
                  <%= source.ai_tutor_file_source&.file_size_human || '0 KB' %>
                </div>
              </div>

              <div class="file-statuses">
                <!-- Training Status -->
                <div class="training-status status-<%= source.training_status || 'new' %>">
                  <% case source.training_status %>
                  <% when 'trained' %>
                    <i class="bi bi-check-circle"></i> Trained
                  <% when 'training' %>
                    <i class="bi bi-clock"></i> Training
                  <% when 'failed' %>
                    <i class="bi bi-exclamation-circle"></i> Failed
                  <% else %>
                    <i class="bi bi-circle"></i> New
                  <% end %>
                </div>

                <div class="file-extraction-status status-<%= source.ai_tutor_file_source&.extraction_status || 'new' %>">
                  <% case source.ai_tutor_file_source&.extraction_status %>
                  <% when 'completed' %>
                    <i class="bi bi-check-circle"></i> Extracted
                  <% when 'processing' %>
                    <i class="bi bi-clock"></i> Extracting
                  <% when 'failed' %>
                    <i class="bi bi-exclamation-circle"></i> Failed
                  <% else %>
                    <i class="bi bi-circle"></i> New
                  <% end %>
                </div>
              </div>
            </div>

            <div class="file-actions">
              <button class="action-btn" type="button" data-bs-toggle="dropdown">
                <i class="bi bi-three-dots"></i>
              </button>
              <ul class="dropdown-menu">
                <li>
                  <a class="dropdown-item text-danger" href="#" onclick="deleteSource(<%= source.id %>, event)">
                    <i class="bi bi-trash"></i> Delete
                  </a>
                </li>
              </ul>
            </div>

            <button class="expand-btn" type="button">
              <i class="bi bi-chevron-right"></i>
            </button>
          </div>
        <% end %>
      <% else %>
        <div class="empty-files-state">
          <div class="empty-icon">
            <i class="bi bi-file-earmark"></i>
          </div>
          <p>No files uploaded yet. Upload your first file to get started.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const uploadArea = document.getElementById('file-upload-area');
  const fileInput = document.getElementById('file-input');
  const uploadForm = document.getElementById('file-upload-form');
  const hiddenTitle = document.getElementById('hidden-title');

  // Click to select file
  uploadArea.addEventListener('click', () => {
    fileInput.click();
  });

  // Drag and drop
  uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
  });

  uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
  });

  uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      fileInput.files = files;
      handleFileSelect(files[0]);
    }
  });

  // File input change - auto upload
  fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
      handleFileSelect(e.target.files[0]);
    }
  });

  function handleFileSelect(file) {
    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ];

    if (!allowedTypes.includes(file.type)) {
      alert('Unsupported file type. Please select a PDF, DOC, DOCX, TXT, CSV, XLS, XLSX, PPT, or PPTX file.');
      fileInput.value = '';
      return;
    }

    // Validate file size (50MB)
    if (file.size > 50 * 1024 * 1024) {
      alert('File size too large. Please select a file smaller than 50MB.');
      fileInput.value = '';
      return;
    }

    // Auto-generate title from filename
    const fileName = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
    hiddenTitle.value = fileName;

    // Show upload area as loading (but keep file input)
    const uploadContent = uploadArea.querySelector('.upload-content');
    uploadContent.innerHTML = `
      <div class="upload-icon">
        <i class="bi bi-hourglass-split"></i>
      </div>
      <div class="upload-text">
        <p class="upload-main">Uploading ${file.name}...</p>
        <p class="upload-sub">${formatFileSize(file.size)}</p>
      </div>
    `;

    // Auto submit form
    setTimeout(() => {
      uploadForm.submit();
    }, 500);
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }
});
</script>
