<script>
  let textEditor, qaEditor;

  document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('text-editor') !== null) {
      // Initialize CKEditor for text section
      ClassicEditor
        .create(document.querySelector('#text-editor'))
        .then(editor => {
          textEditor = editor;
        })
        .catch(error => {
          console.error(error);
        });
    }

    if (document.getElementById('qa-editor') !== null) {
      // Initialize CKEditor for Q&A section
      ClassicEditor
        .create(document.querySelector('#qa-editor'))
        .then(editor => {
          qaEditor = editor;
        })
        .catch(error => {
          console.error(error);
        });
    }

    // No need for sidebar navigation since we're using URL routing

    // Form submissions
    if (document.getElementById('text-form') !== null) {
      document.getElementById('text-form').addEventListener('submit', function(e) {
        e.preventDefault();
        submitTextForm();
      });
    }

    if (document.getElementById('website-form') !== null) {
      document.getElementById('website-form').addEventListener('submit', function(e) {
        e.preventDefault();
        submitWebsiteForm();
      });
    }

    if (document.getElementById('qa-form') !== null) {
      document.getElementById('qa-form').addEventListener('submit', function(e) {
        e.preventDefault();
        submitQAForm();
      });
    }

    // Handle crawl type tabs
    document.querySelectorAll('.crawl-tab').forEach(tab => {
      tab.addEventListener('click', function() {
        const crawlType = this.dataset.type;
        navigateToCrawlType(crawlType);
      });
    });
  });

  function navigateToCrawlType(crawlType) {
    // Navigate to new URL with crawl_type parameter
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('crawl_type', crawlType);
    const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
    window.location.href = newUrl;
  }

  // switchCrawlType function removed - now handled by page navigation with URL params

  function submitTextForm() {
    const title = document.getElementById('text-title').value;
    const content = textEditor.getData();

    console.log('Submitting text form:', { title, content });

    if (!title || !content) {
      alert('Please fill in both title and content');
      return;
    }

    // Create form data
    const formData = new FormData();
    formData.append('ai_tutor_source[title]', title);
    formData.append('ai_tutor_source[content]', content);
    formData.append('ai_tutor_source[source_type]', 'text');

    console.log('Form data created:', Array.from(formData.entries()));

    // Submit to server
    fetch('<%= admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent) %>.json', {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        'Accept': 'application/json'
      },
      body: formData
    })
    .then(response => {
      console.log('Response status:', response.status);
      return response.json();
    })
    .then(data => {
      console.log('Response data:', data);
      if (data.success) {
        location.reload();
      } else {
        alert('Error: ' + data.error);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('An error occurred while submitting the form');
    });
  }

  function submitWebsiteForm() {
    const url = document.getElementById('website-url').value;
    // Get crawl type from active tab instead of URL params
    const activeTab = document.querySelector('.crawl-tab.active');
    const crawlType = activeTab ? activeTab.dataset.type : 'crawl_links';
    const includePaths = document.getElementById('include-paths')?.value || '';
    const excludePaths = document.getElementById('exclude-paths')?.value || '';
    const maxPages = document.getElementById('max-pages')?.value || document.getElementById('sitemap-max-pages')?.value || 50;

    if (!url) {
      alert('Please enter a URL');
      return;
    }

    // Create form data
    const formData = new FormData();
    formData.append('ai_tutor_source[title]', url);
    formData.append('ai_tutor_source[source_type]', 'web');
    formData.append('url', url);
    formData.append('crawl_type', crawlType);
    formData.append('include_paths', includePaths);
    formData.append('exclude_paths', excludePaths);
    formData.append('max_pages', maxPages);

    console.log('Submitting website form:', { url, crawlType, includePaths, excludePaths, maxPages });

    // Submit to server
    fetch('<%= admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent) %>.json', {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        'Accept': 'application/json'
      },
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('Error: ' + data.error);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('An error occurred while submitting the form');
    });
  }

  function submitQAForm() {
    const title = document.getElementById('qa-title').value;
    const questions = Array.from(document.querySelectorAll('.question-input')).map(input => input.value).filter(q => q);
    const answer = qaEditor.getData();

    if (!title || questions.length === 0 || !answer) {
      alert('Please fill in title, at least one question, and answer');
      return;
    }

    // Create form data
    const formData = new FormData();
    formData.append('ai_tutor_source[title]', title);
    formData.append('ai_tutor_source[content]', answer);
    formData.append('ai_tutor_source[source_type]', 'qa');

    // Add questions as array
    questions.forEach((question, index) => {
      formData.append(`ai_tutor_source[questions][]`, question);
    });

    // Submit to server
    fetch('<%= admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent) %>.json', {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        'Accept': 'application/json'
      },
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('Error: ' + data.error);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('An error occurred while submitting the form');
    });
  }

  function addQuestion() {
    const container = document.getElementById('questions-container');
    const questionGroup = document.createElement('div');
    questionGroup.className = 'question-group';
    questionGroup.innerHTML = `
      <input type="text" class="form-control question-input" placeholder="Enter another question">
      <button type="button" class="remove-question-btn" onclick="removeQuestion(this)">×</button>
    `;
    container.appendChild(questionGroup);
  }

  function removeQuestion(button) {
    const questionGroup = button.parentElement;
    const container = document.getElementById('questions-container');

    // Don't remove if it's the last question
    if (container.children.length > 1) {
      questionGroup.remove();
    }
  }

  function editSource(sourceId) {
    // Implement edit functionality
    console.log('Edit source:', sourceId);
  }

  function deleteSource(sourceId, event) {
    event.preventDefault();
    if (confirm('Are you sure you want to delete this source?')) {
      fetch(`<%= admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent) %>/${sourceId}.json`, {
        method: 'DELETE',
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
          'Accept': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      });
    }
  }

  function recrawlSource(sourceId) {
    if (confirm('Are you sure you want to recrawl this source?')) {
      fetch(`<%= admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent) %>/${sourceId}/recrawl.json`, {
        method: 'POST',
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
          'Accept': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('Recrawl started successfully');
          location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while starting the recrawl');
      });
    }
  }

  function trainAiAgent() {
    const button = document.querySelector('.btn-train-ai');

    // Check if button is disabled (training in progress)
    if (button.disabled) {
      showNotification('Training is already in progress. Please wait for it to complete.', 'warning');
      return;
    }

    // Check if button has training class (additional safety check)
    if (button.classList.contains('training')) {
      showNotification('Training is already in progress. Please wait for it to complete.', 'warning');
      return;
    }

    if (!confirm('Are you sure you want to train the AI agent with all untrained sources? This process may take several minutes.')) {
      return;
    }

    const originalContent = button.innerHTML;

    // Update button to show loading state
    button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Starting Training...';
    button.disabled = true;
    button.classList.add('training');

    // Submit training request
    fetch('<%= admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent) %>/train', {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show success message
        showNotification('Training started successfully! Sources are being processed in the background.', 'success');

        // Reload page after short delay to show updated status
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        throw new Error(data.error || 'Training failed');
      }
    })
    .catch(error => {
      console.error('Training error:', error);
      showNotification('Failed to start training: ' + error.message, 'error');

      // Restore button only if training failed to start
      button.innerHTML = originalContent;
      button.disabled = false;
      button.classList.remove('training');
    });
  }

  function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info-circle'}"></i>
        <span>${message}</span>
      </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
      notification.remove();
    }, 5000);
  }
</script>
