<style>
  .sources-layout {
    display: flex;
    min-height: calc(100vh - 200px);
    gap: 0;
  }

  .sources-sidebar {
    width: 150px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    padding: 0;
  }

  .sources-main {
    flex: 1;
    background: white;
    padding: 2rem;
    display: flex;
    gap: 2rem;
  }

  .main-content {
    flex: 1;
  }

  .sources-stats-sidebar {
    max-width: 250px;
    background: #f8f9fa;
    border-left: 1px solid #e9ecef;
    padding: 1.5rem;
  }

  .stats-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 1rem;
  }

  .stats-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
  }

  .stats-item:last-child {
    border-bottom: none;
  }

  .stats-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #374151;
    font-size: 0.875rem;
  }

  .stats-value {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
  }

  .total-size {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
  }

  .total-size-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
  }

  .total-size-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
  }

  .total-size-subtext {
    font-size: 0.75rem;
    color: #9ca3af;
  }

  .retrain-section {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .retrain-btn {
    width: 100%;
    background: #1f2937;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    margin-bottom: 1rem;
  }

  .retrain-btn:hover {
    background: #111827;
  }

  .retrain-note {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #f59e0b;
    background: #fef3c7;
    padding: 0.5rem;
    border-radius: 4px;
  }

  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
  }

  .sidebar-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: #1f2937;
  }

  .sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .sidebar-item {
    border-bottom: 1px solid #e9ecef;
  }

  .sidebar-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s;
    gap: 0.75rem;
  }

  .sidebar-link:hover {
    background: #e5e7eb;
    color: #374151;
    text-decoration: none;
  }

  .sidebar-link.active {
    background: #3b82f6;
    color: white;
  }

  .sidebar-link.active:hover {
    background: #2563eb;
    color: white;
  }

  .sidebar-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .main-header {
    margin-bottom: 2rem;
  }

  .main-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
  }

  .main-subtitle {
    color: #6b7280;
    margin: 0;
  }

  .content-section {
    display: none;
  }

  .content-section.active {
    display: block;
  }

  /* Form styles moved to _form_styles.html.erb */

  /* Button styles moved to _button_styles.html.erb */

  .sources-list {
    margin-top: 2rem;
  }

  .source-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    background: white;
  }

  .source-item:hover {
    background: #f9fafb;
  }

  .source-info {
    flex: 1;
    min-width: 0; /* Allow text truncation */
  }

  /* Source Type Icons */
  .source-icon-container {
    flex-shrink: 0;
    margin-top: 0.125rem; /* Slight alignment adjustment */
  }

  .source-type-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    font-weight: 500;
  }

  .source-type-crawl_links {
    background: #dbeafe;
    color: #1e40af;
  }

  .source-type-sitemap {
    background: #dcfce7;
    color: #166534;
  }

  .source-type-individual_link {
    background: #fef3c7;
    color: #92400e;
  }

  .source-type-default {
    background: #f3f4f6;
    color: #6b7280;
  }

  /* Crawled Links Section */
  .crawled-links-section {
    border-top: 1px solid #e5e7eb;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
  }

  .crawled-links-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 0.5rem 0;
    user-select: none;
  }

  .crawled-links-header:hover {
    background: #f9fafb;
    border-radius: 4px;
    padding: 0.5rem;
    margin: 0 -0.5rem;
  }

  .links-count {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .expand-icon {
    font-size: 1rem;
    color: #6b7280;
    transition: transform 0.2s ease;
  }

  .expand-icon.expanded {
    transform: rotate(180deg);
  }

  .crawled-links-list {
    margin-top: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: #fafafa;
    max-height: 300px;
    overflow-y: auto;
  }

  .crawled-link-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    background: white;
    transition: background-color 0.2s ease;
  }

  .crawled-link-item:last-child {
    border-bottom: none;
  }

  .crawled-link-item:hover {
    background: #f9fafb;
  }

  .crawled-link-url {
    flex: 1;
    min-width: 0;
  }

  .crawled-link-url .external-link {
    color: #374151;
    text-decoration: none;
    font-size: 0.875rem;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 500px;
  }

  .crawled-link-url .external-link:hover {
    color: #3b82f6;
    text-decoration: underline;
  }

  /* Override external-link pseudo-element for crawled links */
  .crawled-link-url .external-link:after {
    content: none !important;
  }

  .crawled-link-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
  }

  .crawled-link-actions .bi-chevron-right {
    color: #d1d5db;
    font-size: 0.875rem;
  }

  /* Training Section */
  .training-section {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .training-stats {
    margin-bottom: 1rem;
  }

  .training-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 1rem 0 0.5rem 0;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid #f3f4f6;
  }

  .training-section-title:first-child {
    margin-top: 0;
  }

  .agent-status-badge {
    float: right;
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .agent-status-not_trained {
    background: #fef3c7;
    color: #92400e;
  }

  .agent-status-training {
    background: #dbeafe;
    color: #1e40af;
  }

  .agent-status-trained {
    background: #dcfce7;
    color: #166534;
  }

  .agent-status-failed {
    background: #fee2e2;
    color: #dc2626;
  }

  .training-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  .training-stat-label {
    color: #6b7280;
  }

  .training-stat-value {
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    font-size: 0.75rem;
  }

  .training-stat-value.not-trained {
    background: #fef3c7;
    color: #92400e;
  }

  .training-stat-value.training {
    background: #dbeafe;
    color: #1e40af;
  }

  .training-stat-value.trained {
    background: #dcfce7;
    color: #166534;
  }

  .training-stat-value.failed {
    background: #fee2e2;
    color: #dc2626;
  }

  .btn-train-ai {
    width: 100%;
    padding: 0.75rem 1rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .btn-train-ai:hover:not(:disabled) {
    background: #2563eb;
    transform: translateY(-1px);
  }

  .btn-train-ai:hover {
    color: white;
    text-decoration: none;
  }

  .btn-train-ai:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    pointer-events: none;
  }

  .btn-train-ai.training {
    background: #6b7280;
  }

  .btn-train-ai.trained {
    background: #10b981;
  }

  .btn-train-ai.failed {
    background: #dc2626;
  }

  .btn-train-ai.failed:hover:not(:disabled) {
    background: #b91c1c;
  }

  .train-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    font-size: 0.75rem;
  }

  .spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  /* Notifications */
  .notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease-out;
  }

  .notification-success {
    background: #dcfce7;
    border: 1px solid #bbf7d0;
    color: #166534;
  }

  .notification-error {
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #dc2626;
  }

  .notification-info {
    background: #dbeafe;
    border: 1px solid #bfdbfe;
    color: #1e40af;
  }

  .notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .source-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.25rem;
  }

  .source-name {
    font-weight: 500;
    color: #1f2937;
  }

  .source-title-link {
    color: #1f2937;
    text-decoration: none;
    transition: color 0.2s;
  }

  .source-title-link:hover {
    color: #3b82f6;
    text-decoration: none;
  }

  /* Website Form Styles */
  .crawl-type-tabs {
    display: flex;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 1rem;
  }

  .crawl-tab {
    flex: 1;
    padding: 0.75rem 1rem;
    background: white;
    border: none;
    border-right: 1px solid #d1d5db;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    text-decoration: none;
  }

  .crawl-tab:last-child {
    border-right: none;
  }

  .crawl-tab:hover {
    background: #f9fafb;
    color: #374151;
    text-decoration: none;
  }

  .crawl-tab.active {
    background: #3b82f6;
    color: white;
  }

  .crawl-tab.active:hover {
    background: #2563eb;
    color: white;
    text-decoration: none;
  }

  .form-help {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #0369a1;
  }

  .crawl-options {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
  }

  /* form-text moved to _form_styles.html.erb */

  .text-muted {
    color: #6b7280 !important;
  }

  /* File-specific styles */
  .file-icon-pdf { color: #dc2626 !important; }
  .file-icon-doc, .file-icon-docx { color: #2563eb !important; }
  .file-icon-xls, .file-icon-xlsx { color: #059669 !important; }
  .file-icon-ppt, .file-icon-pptx { color: #d97706 !important; }
  .file-icon-txt, .file-icon-csv { color: #6b7280 !important; }

  .extraction-status {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    font-weight: 500;
  }

  .status-completed {
    background: #dcfce7;
    color: #166534;
  }

  .status-processing {
    background: #fef3c7;
    color: #92400e;
  }

  .status-failed {
    background: #fee2e2;
    color: #991b1b;
  }

  .status-pending {
    background: #f3f4f6;
    color: #6b7280;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
  }

  .page-title h1 {
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: #1f2937;
  }

  .page-description {
    color: #6b7280;
    margin: 0;
    font-size: 1rem;
  }

  .btn-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #374151;
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s;
  }

  .btn-info:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  /* Add Files Section */
  .add-files-section {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 2rem;
    background: white;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .collapse-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .collapse-btn:hover {
    background: #f3f4f6;
    color: #374151;
  }

  .pdf-warning {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    padding: 0.75rem 1rem;
    margin: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #92400e;
    font-size: 0.875rem;
  }

  .pdf-warning i {
    color: #f59e0b;
  }

  /* Upload Area */
  .file-upload-form {
    padding: 0 1.5rem 1.5rem;
  }

  .upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    background: #f9fafb;
    margin-bottom: 1rem;
  }

  .upload-area:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  .upload-area.dragover {
    border-color: #3b82f6;
    background: #dbeafe;
  }

  .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .upload-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6b7280;
  }

  .upload-text {
    text-align: center;
  }

  .upload-main {
    font-size: 1rem;
    color: #374151;
    margin: 0 0 0.25rem 0;
    font-weight: 500;
  }

  .upload-sub {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }

  /* File Preview */
  .file-preview {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
  }

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .file-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .file-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #6b7280;
  }

  .file-details .file-name {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.25rem;
  }

  .file-details .file-size {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .remove-file-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .remove-file-btn:hover {
    background: #fee2e2;
    color: #dc2626;
  }

  /* Upload Progress */
  .upload-progress {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }

  .progress-fill {
    height: 100%;
    background: #3b82f6;
    width: 0%;
    transition: width 0.3s ease;
  }

  .progress-text {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
  }

  /* Upload Status */
  .upload-status {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
  }

  .status-message {
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .status-message.success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }

  .status-message.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }

  .status-message.info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
  }

  /* Form Fields */
  .form-fields {
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
    margin-top: 1rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  .form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s;
  }

  .form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
  }

  .btn-primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  .btn-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
  }

  .btn-secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;
  }

  .btn-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  /* File Sources Section */
  .file-sources-section {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
  }

  .section-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .search-box {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-box i {
    position: absolute;
    left: 0.75rem;
    color: #6b7280;
    font-size: 0.875rem;
  }

  .search-input {
    padding: 0.5rem 0.75rem 0.5rem 2rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    width: 200px;
  }

  .search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .sort-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
  }

  .sort-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    background: white;
  }

  /* File List Controls */
  .file-list-controls {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .select-all-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
  }

  .select-all-checkbox input[type="checkbox"] {
    display: none;
  }

  .checkmark {
    width: 16px;
    height: 16px;
    border: 1px solid #d1d5db;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
  }

  .select-all-checkbox input[type="checkbox"]:checked + .checkmark {
    background: #3b82f6;
    border-color: #3b82f6;
  }

  .select-all-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: "✓";
    color: white;
    font-size: 10px;
    font-weight: bold;
  }

  .file-source-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s;
  }

  .file-source-item:hover {
    background: #f9fafb;
  }

  .file-source-item:last-child {
    border-bottom: none;
  }

  .file-checkbox {
    margin-right: 1rem;
    cursor: pointer;
  }

  .file-checkbox input[type="checkbox"] {
    display: none;
  }

  .file-content {
    flex: 1;
    min-width: 0;
  }

  .file-main-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
  }

  .file-name {
    font-weight: 500;
    color: #1f2937;
    font-size: 0.875rem;
    truncate: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .file-size {
    font-size: 0.75rem;
    color: #6b7280;
    margin-left: 1rem;
  }

  .file-status {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    font-weight: 500;
    display: inline-block;
  }

  .file-status.status-completed {
    background: #dcfce7;
    color: #166534;
  }

  .file-status.status-processing {
    background: #fef3c7;
    color: #92400e;
  }

  .file-status.status-failed {
    background: #fee2e2;
    color: #991b1b;
  }

  .file-status.status-pending {
    background: #f3f4f6;
    color: #6b7280;
  }

  .file-actions {
    position: relative;
    margin-left: 1rem;
  }

  .action-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .action-btn:hover {
    background: #f3f4f6;
    color: #374151;
  }

  .expand-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: 0.5rem;
    transition: all 0.2s;
  }

  .expand-btn:hover {
    color: #374151;
  }

  /* Empty State */
  .empty-files-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6b7280;
  }

  .empty-files-state .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #d1d5db;
  }

  .empty-files-state p {
    margin: 0;
    font-size: 0.875rem;
  }

  /* Training Status Badges */
  .training-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.5rem;
  }

  .training-status.status-trained {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }

  .training-status.status-training {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
  }

  .training-status.status-failed {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }

  .training-status.status-new {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
  }

  .file-extraction-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.5rem;
  }

  .file-extraction-status.status-completed {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }

  .file-extraction-status.status-processing {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
  }

  .file-extraction-status.status-failed {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }

  .file-extraction-status.status-new {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
  }

  .file-extraction-status.status-pending {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
  }

  /* File Statuses Container */
  .file-statuses {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .file-statuses .file-status {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    font-weight: 500;
  }

  /* Source Statuses Container for Website */
  .source-statuses {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .source-statuses .status-badge,
  .source-statuses .training-status {
    margin-top: 0;
  }

  .status-not_trained {
    background: #D3D3D3;
  }

  .form-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .or-divider {
    color: #6b7280;
    font-size: 0.875rem;
    position: relative;
  }

  .or-divider::before,
  .or-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 50px;
    height: 1px;
    background: #e5e7eb;
  }

  .or-divider::before {
    right: 100%;
    margin-right: 10px;
  }

  .or-divider::after {
    left: 100%;
    margin-left: 10px;
  }

  .new-page-links {
    text-align: center;
  }

  .new-page-label {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }

  .new-page-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  /* btn-outline-primary moved to _button_styles.html.erb */

  /* Status Badges */
  .status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .status-pending {
    background: #fef3c7;
    color: #92400e;
  }

  .status-crawling {
    background: #dbeafe;
    color: #1e40af;
  }

  .status-completed {
    background: #dcfce7;
    color: #166534;
  }

  .status-failed {
    background: #fee2e2;
    color: #dc2626;
  }

  /* Spinning animation for crawling status */
  .spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  /* Dropdown improvements */
  .dropdown-menu {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
  }

  .dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #374151;
    text-decoration: none;
  }

  .dropdown-item:hover {
    background: #f9fafb;
    color: #374151;
    text-decoration: none;
  }

  .dropdown-item.text-danger {
    color: #dc2626;
  }

  .dropdown-item.text-danger:hover {
    background: #fee2e2;
    color: #dc2626;
  }

  .dropdown-divider {
    margin: 0.5rem 0;
    border-color: #e5e7eb;
  }



  .source-meta {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .source-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
    align-items: flex-start;
    margin-top: 0.125rem; /* Align with icon */
  }

  /* btn-sm moved to _button_styles.html.erb */

  .editor-container {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    overflow: hidden;
  }

  .ck-editor__editable {
    min-height: 200px;
  }

  .question-group {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
  }

  .question-input {
    margin-bottom: 0.5rem;
    padding-right: 3rem; /* Make space for remove button */
  }

  .add-question-btn {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    font-size: 0.875rem;
    padding: 0.5rem 0;
    transition: all 0.2s;
  }

  .add-question-btn:hover {
    text-decoration: underline;
    color: #2563eb;
  }

  .remove-question-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #dc2626;
    cursor: pointer;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s;
    line-height: 1;
  }

  .remove-question-btn:hover {
    background: #fecaca;
    border-color: #f87171;
    color: #b91c1c;
    transform: scale(1.1);
  }

  .remove-question-btn:active {
    transform: scale(0.95);
  }

  /* Responsive design */
  @media (max-width: 1200px) {
    .sources-layout {
      flex-direction: column;
    }

    .sources-sidebar {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #e9ecef;
    }

    .sidebar-menu {
      display: flex;
      overflow-x: auto;
    }

    .sidebar-item {
      border-bottom: none;
      border-right: 1px solid #e9ecef;
      flex-shrink: 0;
    }

    .sources-main {
      flex-direction: column;
      padding: 1rem;
    }

    .sources-stats-sidebar {
      width: 100%;
      border-left: none;
      border-top: 1px solid #e9ecef;
      margin-top: 2rem;
    }
  }

  @media (max-width: 768px) {
    .sources-main {
      padding: 0.5rem;
    }

    .main-content {
      padding: 0;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .row {
      margin: 0;
    }

    .col-md-6 {
      padding: 0;
      margin-bottom: 1rem;
    }
  }
</style>
