<%= form_with model: [@school, @ai_tutor_agent, @ai_tutor_source], local: true, multipart: true do |form| %>
  <%= form.hidden_field :source_type, value: 'file' %>
  
  <!-- File Upload Area -->
  <div class="form-group">
    <label class="form-label">Upload File</label>
    <div class="upload-area" id="file-upload-area">
      <div class="upload-icon">
        <i class="bi bi-cloud-upload"></i>
      </div>
      <div class="upload-text">
        <h3>Upload File</h3>
        <p>Drag & drop a file here, or click to select</p>
        <small>Supported: PDF, DOC, DOCX, TXT, CSV, XLS, XLSX, PPT, PPTX</small>
        <small>Maximum size: 10MB</small>
      </div>
      <%= form.file_field :file, 
                         id: "file-input",
                         accept: ".pdf,.doc,.docx,.txt,.csv,.xls,.xlsx,.ppt,.pptx",
                         style: "display: none;" %>
    </div>

    <div class="file-preview" id="file-preview" style="display: none;">
      <div class="file-info">
        <div class="file-icon">
          <i class="bi bi-file-earmark"></i>
        </div>
        <div class="file-details">
          <div class="file-name"></div>
          <div class="file-size"></div>
          <div class="file-type"></div>
        </div>
        <button type="button" class="remove-file-btn" id="remove-file-btn">
          <i class="bi bi-x"></i>
        </button>
      </div>
    </div>

    <% if @ai_tutor_source.errors[:file].any? %>
      <div class="error-message">
        <%= @ai_tutor_source.errors[:file].first %>
      </div>
    <% end %>
  </div>

  <!-- Title -->
  <div class="form-group">
    <%= form.label :title, class: "form-label" %>
    <%= form.text_field :title, 
                       class: "form-control #{'is-invalid' if @ai_tutor_source.errors[:title].any?}",
                       placeholder: "Enter a descriptive title for this file" %>
    <% if @ai_tutor_source.errors[:title].any? %>
      <div class="error-message">
        <%= @ai_tutor_source.errors[:title].first %>
      </div>
    <% end %>
    <div class="form-text">
      Choose a clear, descriptive title that helps identify this file content.
    </div>
  </div>

  <!-- Description -->
  <div class="form-group">
    <%= form.label :description, class: "form-label" %>
    <%= form.text_area :description, 
                      class: "form-control #{'is-invalid' if @ai_tutor_source.errors[:description].any?}",
                      rows: 3,
                      placeholder: "Add a description to help understand the file content..." %>
    <% if @ai_tutor_source.errors[:description].any? %>
      <div class="error-message">
        <%= @ai_tutor_source.errors[:description].first %>
      </div>
    <% end %>
    <div class="form-text">
      Provide additional context about what this file contains and how it should be used.
    </div>
  </div>

  <!-- Form Actions -->
  <div class="form-actions">
    <%= form.submit "Upload File", class: "btn btn-primary" %>
    <%= link_to "Cancel", admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent),
                class: "btn btn-secondary" %>
  </div>
<% end %>

<style>
  .upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    background: var(--bg-secondary);
    margin-bottom: 1rem;
  }

  .upload-area:hover {
    border-color: var(--accent-color);
    background: rgba(59, 130, 246, 0.05);
  }

  .upload-area.dragover {
    border-color: var(--accent-color);
    background: rgba(59, 130, 246, 0.1);
  }

  .upload-icon {
    font-size: 3rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
  }

  .upload-text h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-weight: 600;
  }

  .upload-text p {
    margin: 0 0 1rem 0;
    color: var(--text-secondary);
  }

  .upload-text small {
    display: block;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
  }

  .file-preview {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    background: var(--bg-secondary);
    margin-bottom: 1rem;
  }

  .file-info {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .file-icon {
    font-size: 2rem;
    color: var(--accent-color);
    flex-shrink: 0;
  }

  .file-details {
    flex: 1;
  }

  .file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
  }

  .file-size, .file-type {
    font-size: 0.875rem;
    color: var(--text-secondary);
  }

  .remove-file-btn {
    background: none;
    border: none;
    color: #dc2626;
    cursor: pointer;
    font-size: 1.25rem;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .remove-file-btn:hover {
    background: #fee2e2;
  }

  /* File type specific icons */
  .file-icon.pdf { color: #dc2626; }
  .file-icon.doc, .file-icon.docx { color: #2563eb; }
  .file-icon.xls, .file-icon.xlsx { color: #059669; }
  .file-icon.ppt, .file-icon.pptx { color: #d97706; }
  .file-icon.txt, .file-icon.csv { color: #6b7280; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const uploadArea = document.getElementById('file-upload-area');
  const fileInput = document.getElementById('file-input');
  const filePreview = document.getElementById('file-preview');
  const removeFileBtn = document.getElementById('remove-file-btn');

  // Click to select file
  uploadArea.addEventListener('click', () => {
    fileInput.click();
  });

  // Drag and drop
  uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
  });

  uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
  });

  uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      fileInput.files = files;
      handleFileSelect(files[0]);
    }
  });

  // File input change
  fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
      handleFileSelect(e.target.files[0]);
    }
  });

  // Remove file
  removeFileBtn.addEventListener('click', () => {
    fileInput.value = '';
    filePreview.style.display = 'none';
    uploadArea.style.display = 'block';
  });

  function handleFileSelect(file) {
    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ];

    if (!allowedTypes.includes(file.type)) {
      alert('Unsupported file type. Please select a PDF, DOC, DOCX, TXT, CSV, XLS, XLSX, PPT, or PPTX file.');
      fileInput.value = '';
      return;
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size too large. Please select a file smaller than 10MB.');
      fileInput.value = '';
      return;
    }

    // Show file preview
    displayFilePreview(file);
    
    // Auto-fill title if empty
    const titleInput = document.querySelector('input[name="ai_tutor_source[title]"]');
    if (!titleInput.value) {
      titleInput.value = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
    }
  }

  function displayFilePreview(file) {
    const fileName = filePreview.querySelector('.file-name');
    const fileSize = filePreview.querySelector('.file-size');
    const fileType = filePreview.querySelector('.file-type');
    const fileIcon = filePreview.querySelector('.file-icon i');

    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileType.textContent = file.type;

    // Set appropriate icon
    const extension = file.name.split('.').pop().toLowerCase();
    fileIcon.className = `bi bi-file-earmark-${getFileIcon(extension)}`;
    fileIcon.parentElement.className = `file-icon ${extension}`;

    uploadArea.style.display = 'none';
    filePreview.style.display = 'block';
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  function getFileIcon(extension) {
    const iconMap = {
      'pdf': 'pdf',
      'doc': 'word',
      'docx': 'word',
      'xls': 'excel',
      'xlsx': 'excel',
      'ppt': 'ppt',
      'pptx': 'ppt',
      'txt': 'text',
      'csv': 'text'
    };
    return iconMap[extension] || 'text';
  }
});
</script>
