class Admin::AiTutorSourcesController < Admin::ApplicationController
  before_action :set_school
  before_action :set_ai_tutor_agent
  before_action :set_ai_tutor_source, only: [:show, :edit, :update, :destroy]

  def index
    @crawl_type = params[:crawl_type] || 'crawl_links'
    @sources = @ai_tutor_agent.ai_tutor_sources.includes(:ai_tutor_text_source, :ai_tutor_web_source, :ai_tutor_qa_source)
                              .order(created_at: :desc)

    # Filter by source_type if specified
    if params[:source_type].present?
      # Map 'website' to 'web' for consistency
      source_type = params[:source_type] == 'website' ? 'web' : params[:source_type]
      @sources = @sources.by_type(source_type)

      # Don't filter by crawl_type - show all web sources
      # Users can see all crawl types in one view
    end

    # Get statistics
    @sources_stats = @ai_tutor_agent.sources_usage_stats
    @sources_by_type = @ai_tutor_agent.sources_summary

    # Pagination
    @sources = @sources.page(params[:page]).per(10)

    respond_to do |format|
      format.html
      format.json { render json: sources_json }
    end
  end

  def show
    respond_to do |format|
      format.html
      format.json { render json: source_json(@ai_tutor_source) }
    end
  end

  def new
    @ai_tutor_source = @ai_tutor_agent.ai_tutor_sources.build
    @source_type = params[:type] || 'text'
    @crawl_type = params[:crawl_type] || 'crawl_links'
    @ai_tutor_source.source_type = @source_type
  end

  def create
    @ai_tutor_source = @ai_tutor_agent.ai_tutor_sources.build(source_params)
    @ai_tutor_source.school = @school

    # Handle different source types
    case @ai_tutor_source.source_type
    when 'text'
      if params[:ai_tutor_source][:content].present?
        @ai_tutor_source.build_ai_tutor_text_source(
          content: params[:ai_tutor_source][:content],
          format: 'html'
        )
      else
        render json: { success: false, error: 'Content is required for text sources' }, status: :unprocessable_entity
        return
      end
    when 'web'
      # Handle both AJAX form (from index) and regular form (from new page)
      url = params[:ai_tutor_source]&.[](:url) || params[:url]

      if url.present?
        crawl_type = params[:ai_tutor_source]&.[](:crawl_type) || params[:crawl_type] || 'crawl_links'
        include_paths_str = params[:ai_tutor_source]&.[](:include_paths) || params[:include_paths]
        exclude_paths_str = params[:ai_tutor_source]&.[](:exclude_paths) || params[:exclude_paths]
        max_pages = (params[:ai_tutor_source]&.[](:max_pages) || params[:max_pages])&.to_i || 50

        include_paths = include_paths_str.present? ? include_paths_str.split(',').map(&:strip) : []
        exclude_paths = exclude_paths_str.present? ? exclude_paths_str.split(',').map(&:strip) : []

        @ai_tutor_source.build_ai_tutor_web_source(
          url: url,
          crawl_type: crawl_type,
          include_paths: include_paths,
          exclude_paths: exclude_paths,
          max_pages: max_pages
        )
      else
        error_msg = 'URL is required for web sources'
        respond_to do |format|
          format.html { redirect_to admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent, source_type: 'web'), alert: error_msg }
          format.json { render json: { success: false, error: error_msg }, status: :unprocessable_entity }
        end
        return
      end
    when 'qa'
      if params[:ai_tutor_source][:questions].present? && params[:ai_tutor_source][:content].present?
        @ai_tutor_source.build_ai_tutor_qa_source(
          questions: params[:ai_tutor_source][:questions],
          answer: params[:ai_tutor_source][:content]
        )
      else
        render json: { success: false, error: 'Questions and answer are required for Q&A sources' }, status: :unprocessable_entity
        return
      end
    when 'file'
      uploaded_file = params[:ai_tutor_source][:file] || params[:file]
      if uploaded_file.present?
        @ai_tutor_source.build_ai_tutor_file_source(
          filename: uploaded_file.original_filename,
          file_size: uploaded_file.size,
          file_type: uploaded_file.content_type,
          file_path: uploaded_file
        )
      else
        redirect_to admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent, source_type: 'file'),
                    notice: 'File is required for file sources'
        return
      end
    end

    if @ai_tutor_source.save
      Rails.logger.info "Source saved successfully: #{@ai_tutor_source.id}"
      respond_to do |format|
        format.html {
          redirect_to admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent, source_type: @ai_tutor_source.source_type),
                      notice: 'ソースが正常に作成されました。'
        }
        format.json { render json: { success: true, source: source_json(@ai_tutor_source) }, status: :created }
        format.all { render json: { success: true, source: source_json(@ai_tutor_source) }, status: :created }
      end
    else
      Rails.logger.error "Source save failed: #{@ai_tutor_source.errors.full_messages}"
      respond_to do |format|
        format.html { render :new }
        format.json { render json: { success: false, error: @ai_tutor_source.errors.full_messages.join(', ') }, status: :unprocessable_entity }
        format.all { render json: { success: false, error: @ai_tutor_source.errors.full_messages.join(', ') }, status: :unprocessable_entity }
      end
    end
  end

  def show
    respond_to do |format|
      format.html
      format.json { render json: source_json(@ai_tutor_source) }
    end
  end

  def edit
  end

  def update
    if @ai_tutor_source.update(source_params)
      # Update text content if it's a text source
      if @ai_tutor_source.source_type == 'text' && params[:content].present?
        update_text_content(@ai_tutor_source, params[:content], params[:format] || 'html')
      end

      respond_to do |format|
        format.html {
          redirect_to admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent),
                      notice: 'ソースが正常に更新されました。'
        }
        format.json { render json: source_json(@ai_tutor_source) }
      end
    else
      respond_to do |format|
        format.html { render :edit }
        format.json { render json: { errors: @ai_tutor_source.errors }, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @ai_tutor_source.destroy

    respond_to do |format|
      format.html {
        redirect_to admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent),
                    notice: 'ソースが正常に削除されました。'
      }
      format.json { render json: { success: true } }
    end
  end

  def recrawl
    if @ai_tutor_source.source_type == 'web' && @ai_tutor_source.ai_tutor_web_source
      if @ai_tutor_source.ai_tutor_web_source.start_crawl!
        respond_to do |format|
          format.json { render json: { success: true, message: 'Recrawl started successfully' } }
        end
      else
        respond_to do |format|
          format.json { render json: { success: false, error: 'Cannot start recrawl - already in progress' }, status: :unprocessable_entity }
        end
      end
    else
      respond_to do |format|
        format.json { render json: { success: false, error: 'Invalid source type for recrawling' }, status: :unprocessable_entity }
      end
    end
  end

  # Bulk actions
  def bulk_destroy
    source_ids = params[:source_ids] || []
    sources = @ai_tutor_agent.ai_tutor_sources.where(id: source_ids)

    destroyed_count = sources.count
    sources.destroy_all

    respond_to do |format|
      format.html {
        redirect_to admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent),
                    notice: "#{destroyed_count}個のソースが削除されました。"
      }
      format.json { render json: { message: "#{destroyed_count} sources deleted" } }
    end
  end

  def bulk_toggle_enabled
    source_ids = params[:source_ids] || []
    enabled = params[:enabled] == 'true'

    sources = @ai_tutor_agent.ai_tutor_sources.where(id: source_ids)
    sources.update_all(enabled: enabled)

    respond_to do |format|
      format.html {
        redirect_to admin_school_ai_tutor_agent_ai_tutor_sources_path(@school, @ai_tutor_agent),
                    notice: "ソースのステータスが更新されました。"
      }
      format.json { render json: { message: "Sources status updated" } }
    end
  end

  def train
    begin
      # Check if agent or any sources are already training
      if @ai_tutor_agent.training? || @ai_tutor_agent.any_sources_training?
        render json: {
          success: false,
          error: 'Training is already in progress. Please wait for it to complete.'
        }, status: :unprocessable_entity
        return
      end

      # Get all untrained sources that can be trained
      untrained_sources = @ai_tutor_agent.ai_tutor_sources.not_trained.select(&:can_train?)

      if untrained_sources.empty?
        render json: {
          success: false,
          error: 'No sources available for training'
        }, status: :unprocessable_entity
        return
      end

      # Start training for each source
      trained_count = 0
      untrained_sources.each do |source|
        begin
          source.start_training!
          trained_count += 1
        rescue => e
          Rails.logger.error "Failed to start training for source #{source.id}: #{e.message}"
        end
      end

      if trained_count > 0
        render json: {
          success: true,
          message: "Training started for #{trained_count} sources",
          trained_count: trained_count
        }
      else
        render json: {
          success: false,
          error: 'Failed to start training for any sources'
        }, status: :unprocessable_entity
      end

    rescue => e
      Rails.logger.error "Training error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end
  end

  private

  def set_ai_tutor_agent
    @ai_tutor_agent = @school.ai_tutor_agents.find(params[:ai_tutor_agent_id])
  end

  def set_ai_tutor_source
    @ai_tutor_source = @ai_tutor_agent.ai_tutor_sources.find(params[:id])
  end

  def source_params
    params.require(:ai_tutor_source).permit(:source_type, :title, :description, :enabled)
  end

  def update_text_content(source, content, format)
    return unless source.source_type == 'text'

    text_source = source.ai_tutor_text_source || source.create_ai_tutor_text_source!
    text_source.update!(content: content, format: format)
  end

  def sources_json
    {
      sources: @sources.map { |source| source_json(source) },
      stats: @sources_stats,
      by_type: @sources_by_type,
      pagination: {
        current_page: @sources.current_page,
        total_pages: @sources.total_pages,
        total_count: @sources.total_count
      }
    }
  end

  def source_json(source)
    {
      id: source.id,
      source_type: source.source_type,
      title: source.title,
      description: source.description,
      enabled: source.enabled,
      usage_count: source.usage_count,
      last_used_at: source.last_used_at,
      created_at: source.created_at,
      updated_at: source.updated_at,
      content_preview: source.content_preview,
      content_stats: source.content_stats,
      icon_class: source.icon_class,
      color_class: source.color_class
    }
  end
end
