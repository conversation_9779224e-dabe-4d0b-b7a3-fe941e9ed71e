class AiTutorFileSource < ApplicationRecord
  belongs_to :ai_tutor_source

  # File upload
  mount_uploader :file_path, FileUploader

  # Supported file types (similar to Chatbase)
  SUPPORTED_FILE_TYPES = %w[
    application/pdf
    application/msword
    application/vnd.openxmlformats-officedocument.wordprocessingml.document
    text/plain
    text/csv
    application/vnd.ms-excel
    application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    application/vnd.ms-powerpoint
    application/vnd.openxmlformats-officedocument.presentationml.presentation
  ].freeze

  # File extensions mapping
  FILE_EXTENSIONS = {
    'application/pdf' => 'pdf',
    'application/msword' => 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
    'text/plain' => 'txt',
    'text/csv' => 'csv',
    'application/vnd.ms-excel' => 'xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
    'application/vnd.ms-powerpoint' => 'ppt',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx'
  }.freeze

  # Extraction statuses
  EXTRACTION_STATUSES = %w[pending processing completed failed].freeze

  # Validations
  validates :filename, presence: true
  validates :file_type, presence: true, inclusion: { in: SUPPORTED_FILE_TYPES }
  validates :file_size, numericality: { greater_than: 0, less_than: 10.megabytes }
  validates :extraction_status, inclusion: { in: EXTRACTION_STATUSES }
  validates :file_path, presence: true

  # Scopes
  scope :pending_extraction, -> { where(extraction_status: 'pending') }
  scope :processing, -> { where(extraction_status: 'processing') }
  scope :completed, -> { where(extraction_status: 'completed') }
  scope :failed, -> { where(extraction_status: 'failed') }
  scope :by_file_type, ->(type) { where(file_type: type) }

  # Callbacks
  before_save :set_file_metadata
  after_create :queue_extraction_job

  # Instance methods
  def file_extension
    FILE_EXTENSIONS[file_type] || 'unknown'
  end

  def file_size_human
    return '0 B' if file_size.zero?

    units = %w[B KB MB GB]
    size = file_size.to_f
    unit_index = 0

    while size >= 1024 && unit_index < units.length - 1
      size /= 1024
      unit_index += 1
    end

    "#{size.round(1)} #{units[unit_index]}"
  end

  def can_extract?
    pending? && file_path.present?
  end

  def pending?
    extraction_status == 'pending'
  end

  def processing?
    extraction_status == 'processing'
  end

  def completed?
    extraction_status == 'completed'
  end

  def failed?
    extraction_status == 'failed'
  end

  def has_content?
    content.present?
  end

  def file_size_human
    return '0 B' if file_size.nil? || file_size.zero?

    units = ['B', 'KB', 'MB', 'GB']
    size = file_size.to_f
    unit_index = 0

    while size >= 1024 && unit_index < units.length - 1
      size /= 1024
      unit_index += 1
    end

    "#{size.round(1)} #{units[unit_index]}"
  end

  def file_extension
    return '' unless filename.present?
    File.extname(filename).delete('.').upcase
  end

  def content_preview(limit = 200)
    return 'No content extracted' unless has_content?
    content.truncate(limit)
  end

  # Extraction methods
  def start_extraction!
    update!(
      extraction_status: 'processing',
      extraction_error: nil
    )
  end

  def complete_extraction!(extracted_content)
    update!(
      extraction_status: 'completed',
      content: extracted_content,
      extracted_at: Time.current,
      extraction_error: nil
    )
  end

  def fail_extraction!(error_message)
    update!(
      extraction_status: 'failed',
      extraction_error: error_message
    )
  end

  # File type helpers
  def pdf?
    file_type == 'application/pdf'
  end

  def word_document?
    %w[application/msword application/vnd.openxmlformats-officedocument.wordprocessingml.document].include?(file_type)
  end

  def excel_document?
    %w[application/vnd.ms-excel application/vnd.openxmlformats-officedocument.spreadsheetml.sheet].include?(file_type)
  end

  def powerpoint_document?
    %w[application/vnd.ms-powerpoint application/vnd.openxmlformats-officedocument.presentationml.presentation].include?(file_type)
  end

  def text_file?
    %w[text/plain text/csv].include?(file_type)
  end

  private

  def set_file_metadata
    if file_path.present? && file_path_changed?
      self.filename = file_path.file.filename if filename.blank?
      self.file_size = file_path.file.size if file_size.zero?
      self.file_type = file_path.file.content_type if file_type.blank?

      self.metadata ||= {}
      self.metadata['original_filename'] = file_path.file.original_filename
      self.metadata['uploaded_at'] = Time.current.iso8601
    end
  end

  def queue_extraction_job
    FileExtractionWorker.perform_async(id) if can_extract?
  end
end
