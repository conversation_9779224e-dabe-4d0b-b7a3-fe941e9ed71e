class AiTutorAgent < ApplicationRecord
  belongs_to :school

  # Agent types based on the UI mockup
  AGENT_TYPES = %w[dashboard lesson goal exam].freeze

  # Training statuses
  TRAINING_STATUSES = %w[not_trained training trained failed].freeze

  # Associations
  has_many :ai_tutor_prompts, dependent: :destroy
  has_many :ai_tutor_tools, dependent: :destroy
  has_many :ai_tutor_rag_configs, dependent: :destroy
  has_many :ai_tutor_sources, dependent: :destroy

  # Validations
  validates :agent_type, presence: true, inclusion: { in: AGENT_TYPES }
  validates :agent_type, uniqueness: { scope: :school_id }
  validates :name, presence: true
  validates :enabled, inclusion: { in: [true, false] }
  validates :training_status, inclusion: { in: TRAINING_STATUSES }

  # Scopes
  scope :enabled, -> { where(enabled: true) }
  scope :by_type, ->(type) { where(agent_type: type) }
  scope :not_trained, -> { where(training_status: 'not_trained') }
  scope :trained, -> { where(training_status: 'trained') }
  scope :training, -> { where(training_status: 'training') }
  scope :training_failed, -> { where(training_status: 'failed') }

  # Default names for each agent type
  def self.default_name_for(agent_type)
    case agent_type
    when 'dashboard'
      'Dashboard'
    when 'lesson'
      'Lesson'
    when 'goal'
      'Goal'
    when 'exam'
      'Exam'
    else
      agent_type.humanize
    end
  end

  # Default descriptions for each agent type
  def self.default_description_for(agent_type)
    case agent_type
    when 'dashboard'
      'ダッシュボード用のAIエージェント - 学習状況の分析と総合的なサポートを提供'
    when 'lesson'
      'レッスン用のAIエージェント - レッスン中の質問対応と学習サポートを提供'
    when 'goal'
      '目標管理用のAIエージェント - 学習目標の設定と進捗管理をサポート'
    when 'exam'
      '試験用のAIエージェント - 試験対策と問題解決のサポートを提供'
    else
      "#{agent_type.humanize}用のAIエージェント"
    end
  end

  # Create default agents for a school
  def self.create_default_agents_for_school(school)
    AGENT_TYPES.each do |agent_type|
      next if school.ai_tutor_agents.exists?(agent_type: agent_type)

      school.ai_tutor_agents.create!(
        agent_type: agent_type,
        name: default_name_for(agent_type),
        description: default_description_for(agent_type),
        enabled: true
      )
    end
  end

  # Get or create agent for school and type
  def self.find_or_create_for_school_and_type(school, agent_type)
    return nil unless AGENT_TYPES.include?(agent_type)

    school.ai_tutor_agents.find_or_create_by(agent_type: agent_type) do |agent|
      agent.name = default_name_for(agent_type)
      agent.description = default_description_for(agent_type)
      agent.enabled = true
    end
  end

  # Icon mapping for each agent type
  def icon_class
    case agent_type
    when 'dashboard'
      'bi-speedometer2'
    when 'lesson'
      'bi-book'
    when 'goal'
      'bi-target'
    when 'exam'
      'bi-clipboard-check'
    else
      'bi-robot'
    end
  end

  # Color mapping for each agent type
  def color_class
    case agent_type
    when 'dashboard'
      'primary'
    when 'lesson'
      'success'
    when 'goal'
      'warning'
    when 'exam'
      'info'
    else
      'secondary'
    end
  end

  # Check if agent has any configurations
  def configured?
    ai_tutor_prompts.enabled.exists? ||
    ai_tutor_tools.enabled.exists? ||
    ai_tutor_rag_configs.enabled.exists? ||
    ai_tutor_sources.enabled.exists?
  end

  # Get configuration summary
  def configuration_summary
    {
      prompts_count: ai_tutor_prompts.enabled.count,
      tools_count: ai_tutor_tools.enabled.count,
      rag_enabled: ai_tutor_rag_configs.enabled.exists?,
      sources_count: ai_tutor_sources.enabled.count
    }
  end

  # Get sources summary by type
  def sources_summary
    AiTutorSource.total_by_type(self)
  end

  # Get sources usage stats
  def sources_usage_stats
    AiTutorSource.usage_stats(self)
  end

  # Training methods
  def can_train?
    enabled? && !training? && has_trainable_sources?
  end

  def training?
    training_status == 'training'
  end

  def trained?
    training_status == 'trained'
  end

  def training_failed?
    training_status == 'failed'
  end

  def has_trainable_sources?
    ai_tutor_sources.any?(&:can_train?)
  end

  def start_training!
    update!(
      training_status: 'training',
      training_error: nil
    )
  end

  def complete_training!
    update!(
      training_status: 'trained',
      last_trained_at: Time.current,
      training_error: nil
    )
  end

  def fail_training!(error_message)
    update!(
      training_status: 'failed',
      training_error: error_message
    )
  end

  # Get comprehensive training summary
  def training_summary
    all_text_sources = AiTutorTextSource.joins(:ai_tutor_source)
                                       .where(ai_tutor_source: { ai_tutor_agent_id: id })

    {
      agent: {
        training_status: training_status,
        last_trained_at: last_trained_at,
        training_error: training_error
      },
      sources: {
        total: ai_tutor_sources.count,
        not_trained: ai_tutor_sources.not_trained.count,
        training: ai_tutor_sources.training.count,
        trained: ai_tutor_sources.trained.count,
        failed: ai_tutor_sources.training_failed.count
      },
      content_pieces: {
        total: all_text_sources.count,
        not_trained: all_text_sources.not_trained.count,
        training: all_text_sources.training.count,
        trained: all_text_sources.trained.count,
        failed: all_text_sources.training_failed.count
      },
      last_activity: {
        last_source_trained: ai_tutor_sources.joins(:ai_tutor_text_sources)
                                           .where(ai_tutor_text_sources: { training_status: 'trained' })
                                           .maximum('ai_tutor_text_sources.trained_at'),
        last_training_attempt: [last_trained_at,
                               ai_tutor_sources.maximum(:updated_at)].compact.max
      }
    }
  end

  # Check if all sources are trained
  def all_sources_trained?
    ai_tutor_sources.any? && ai_tutor_sources.all? { |source| source.all_text_sources_trained? }
  end

  # Check if any sources are currently training
  def any_sources_training?
    ai_tutor_sources.any? { |source| source.any_text_sources_training? }
  end

  # Get content size statistics by source type
  def content_size_stats
    stats = {}

    # Text sources size
    text_size = ai_tutor_sources.where(source_type: 'text')
                               .joins(:ai_tutor_text_sources)
                               .sum('LENGTH(ai_tutor_text_sources.content)')
    stats['text'] = text_size if text_size > 0

    # Web sources size
    web_size = ai_tutor_sources.where(source_type: 'web')
                              .joins(:ai_tutor_text_sources)
                              .sum('LENGTH(ai_tutor_text_sources.content)')
    stats['web'] = web_size if web_size > 0

    # QA sources size
    qa_size = ai_tutor_sources.where(source_type: 'qa')
                             .joins(:ai_tutor_qa_source)
                             .sum('LENGTH(COALESCE(ai_tutor_qa_sources.answer, "")) + LENGTH(COALESCE(ai_tutor_qa_sources.questions, "[]"))')
    stats['qa'] = qa_size if qa_size > 0

    # File sources size
    file_size = ai_tutor_sources.where(source_type: 'file')
                               .joins(:ai_tutor_text_sources)
                               .sum('LENGTH(ai_tutor_text_sources.content)')
    stats['file'] = file_size if file_size > 0

    # Notion sources size (placeholder - not implemented yet)
    notion_count = ai_tutor_sources.where(source_type: 'notion').count
    if notion_count > 0
      # Estimate notion size as 0 for now since notion source model doesn't exist
      stats['notion'] = 0
    end

    stats
  end

  # Get total content size
  def total_content_size
    content_size_stats.values.sum
  end

  # Get sources count by type
  def sources_count_by_type
    Rails.cache.fetch("agent_#{id}_sources_count_by_type", expires_in: 5.minutes) do
      ai_tutor_sources.group(:source_type).count
    end
  end

  # Clear cached stats
  def clear_stats_cache
    Rails.cache.delete("agent_#{id}_content_size_stats")
    Rails.cache.delete("agent_#{id}_sources_count_by_type")
  end
end
