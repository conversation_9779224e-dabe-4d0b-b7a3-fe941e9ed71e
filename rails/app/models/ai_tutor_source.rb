class AiTutorSource < ApplicationRecord
  belongs_to :ai_tutor_agent
  belongs_to :school

  # Source types
  SOURCE_TYPES = %w[text file web qa notion].freeze

  # Training statuses
  TRAINING_STATUSES = %w[not_trained training trained failed].freeze

  # Serialization
  serialize :vector_ids, Array

  # Associations
  has_one :ai_tutor_text_source, dependent: :destroy
  has_many :ai_tutor_text_sources, dependent: :destroy
  has_one :ai_tutor_web_source, dependent: :destroy
  has_one :ai_tutor_qa_source, dependent: :destroy
  has_one :ai_tutor_file_source, dependent: :destroy

  # Validations
  validates :source_type, presence: true, inclusion: { in: SOURCE_TYPES }
  validates :title, presence: true, length: { maximum: 255 }
  validates :description, length: { maximum: 1000 }
  validates :enabled, inclusion: { in: [true, false] }
  validates :usage_count, numericality: { greater_than_or_equal_to: 0 }
  validates :training_status, inclusion: { in: TRAINING_STATUSES }

  # Scopes
  scope :enabled, -> { where(enabled: true) }
  scope :by_type, ->(type) { where(source_type: type) }
  scope :by_agent, ->(agent) { where(ai_tutor_agent: agent) }
  scope :by_school, ->(school) { where(school: school) }
  scope :recent, -> { order(created_at: :desc) }
  scope :most_used, -> { order(usage_count: :desc) }
  scope :not_trained, -> { where(training_status: 'not_trained') }
  scope :trained, -> { where(training_status: 'trained') }
  scope :training, -> { where(training_status: 'training') }
  scope :training_failed, -> { where(training_status: 'failed') }

  # Callbacks
  before_save :update_metadata
  after_create :create_source_specific_record
  after_commit :clear_agent_stats_cache
  before_destroy :cleanup_pinecone_vectors

  # Instance methods
  def increment_usage!
    increment!(:usage_count)
    touch(:last_used_at)
  end

  def source_data
    case source_type
    when 'text'
      ai_tutor_text_source
    when 'web'
      ai_tutor_web_source
    when 'qa'
      ai_tutor_qa_source
    else
      nil
    end
  end

  def content_preview(limit = 100)
    case source_type
    when 'text'
      ai_tutor_text_source&.content&.truncate(limit)
    when 'web'
      ai_tutor_web_source&.url
    when 'qa'
      ai_tutor_qa_source&.primary_question&.truncate(limit)
    else
      description&.truncate(limit)
    end
  end

  def content_stats
    case source_type
    when 'text'
      {
        character_count: ai_tutor_text_source&.character_count || 0,
        word_count: ai_tutor_text_source&.word_count || 0
      }
    when 'web'
      {
        pages_crawled: ai_tutor_web_source&.pages_crawled || 0,
        crawl_status: ai_tutor_web_source&.crawl_status || 'pending'
      }
    when 'qa'
      {
        questions_count: ai_tutor_qa_source&.questions_count || 0
      }
    else
      {}
    end
  end

  def icon_class
    case source_type
    when 'text'
      'bi-file-text'
    when 'file'
      'bi-file-earmark'
    when 'web'
      'bi-globe'
    when 'qa'
      'bi-question-circle'
    when 'notion'
      'bi-journal-text'
    else
      'bi-file'
    end
  end

  def color_class
    case source_type
    when 'text'
      'primary'
    when 'file'
      'success'
    when 'web'
      'info'
    when 'qa'
      'warning'
    when 'notion'
      'secondary'
    else
      'light'
    end
  end

  # Class methods
  def self.total_by_type(agent_or_school)
    relation = agent_or_school.is_a?(AiTutorAgent) ?
               where(ai_tutor_agent: agent_or_school) :
               where(school: agent_or_school)

    relation.group(:source_type).count
  end

  def self.usage_stats(agent_or_school)
    relation = agent_or_school.is_a?(AiTutorAgent) ?
               where(ai_tutor_agent: agent_or_school) :
               where(school: agent_or_school)

    {
      total_sources: relation.count,
      enabled_sources: relation.enabled.count,
      total_usage: relation.sum(:usage_count),
      last_used: relation.maximum(:last_used_at)
    }
  end

  # Training methods
  def can_train?
    enabled? && !training? && has_content?
  end

  def training?
    training_status == 'training'
  end

  def trained?
    training_status == 'trained'
  end

  def training_failed?
    training_status == 'failed'
  end

  def start_training!
    update!(
      training_status: 'training',
      training_error: nil
    )

    # Queue training job
    AiTrainingWorker.perform_async(id)
  end

  def complete_training!
    update!(
      training_status: 'trained',
      trained_at: Time.current,
      training_error: nil
    )
  end

  def fail_training!(error_message)
    update!(
      training_status: 'failed',
      training_error: error_message
    )
  end

  def has_content?
    case source_type
    when 'text'
      ai_tutor_text_sources.any? { |ts| ts.content.present? }
    when 'web'
      ai_tutor_web_source&.crawled_urls&.any?
    when 'qa'
      ai_tutor_qa_source&.questions&.any? && ai_tutor_qa_source&.answer&.present?
    when 'file'
      ai_tutor_file_source&.has_content?
    else
      true
    end
  end

  def all_text_sources_trained?
    case source_type
    when 'text', 'web'
      ai_tutor_text_sources.any? && ai_tutor_text_sources.all?(&:trained?)
    when 'qa'
      trained? # QA sources don't have individual text sources
    else
      trained?
    end
  end

  def any_text_sources_training?
    case source_type
    when 'text', 'web'
      ai_tutor_text_sources.any?(&:training?)
    else
      training?
    end
  end

  def text_sources_training_summary
    case source_type
    when 'text', 'web'
      {
        total: ai_tutor_text_sources.count,
        not_trained: ai_tutor_text_sources.not_trained.count,
        training: ai_tutor_text_sources.training.count,
        trained: ai_tutor_text_sources.trained.count,
        failed: ai_tutor_text_sources.training_failed.count
      }
    else
      {
        total: 1,
        not_trained: training_status == 'not_trained' ? 1 : 0,
        training: training_status == 'training' ? 1 : 0,
        trained: training_status == 'trained' ? 1 : 0,
        failed: training_status == 'failed' ? 1 : 0
      }
    end
  end

  # Get text sources for specific web source
  def text_sources_for_web_source(web_source)
    return ai_tutor_text_sources.none unless source_type == 'web'
    ai_tutor_text_sources.where(ai_tutor_web_source: web_source)
  end

  # Get web source training summary
  def web_source_training_summary
    return {} unless source_type == 'web' && ai_tutor_web_source

    web_text_sources = text_sources_for_web_source(ai_tutor_web_source)
    {
      web_source_id: ai_tutor_web_source.id,
      url: ai_tutor_web_source.url,
      crawl_type: ai_tutor_web_source.crawl_type,
      total_urls: ai_tutor_web_source.crawled_urls&.length || 0,
      text_sources: {
        total: web_text_sources.count,
        not_trained: web_text_sources.not_trained.count,
        training: web_text_sources.training.count,
        trained: web_text_sources.trained.count,
        failed: web_text_sources.training_failed.count
      }
    }
  end

  private

  def update_metadata
    self.metadata ||= {}
    self.metadata['updated_at'] = Time.current.iso8601
  end

  def create_source_specific_record
    case source_type
    when 'text'
      create_ai_tutor_text_source!(content: '', format: 'html') unless ai_tutor_text_source
    when 'web'
      create_ai_tutor_web_source!(url: '') unless ai_tutor_web_source
    when 'qa'
      create_ai_tutor_qa_source!(questions: [], answer: '') unless ai_tutor_qa_source
    when 'file'
      # File source will be created when file is uploaded
      nil
    end
  end

  def cleanup_pinecone_vectors
    return unless trained? && vector_ids.present?

    begin
      Rails.logger.info "Scheduling cleanup of #{vector_ids.length} Pinecone vectors for source #{id}"

      # Schedule background job for cleanup
      PineconeCleanupWorker.perform_async(
        ai_tutor_agent_id,
        vector_ids,
        id
      )

      Rails.logger.info "Pinecone cleanup job scheduled for source #{id}"
    rescue => e
      Rails.logger.error "Error scheduling Pinecone cleanup for source #{id}: #{e.message}"
      # Don't raise error to prevent deletion from failing
    end
  end
end
