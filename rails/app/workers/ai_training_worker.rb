class AiTrainingWorker
  include Sidekiq::Worker
  sidekiq_options queue: :ai_training, retry: 3

  def perform(ai_tutor_source_id)
    ai_tutor_source = AiTutorSource.find(ai_tutor_source_id)
    ai_tutor_agent = ai_tutor_source.ai_tutor_agent

    Rails.logger.info "Starting AI training for source #{ai_tutor_source_id}: #{ai_tutor_source.title}"

    begin
      ai_tutor_source.update!(training_status: 'training')
      ai_tutor_agent.start_training! unless ai_tutor_agent.training?
      content_chunks = extract_content_chunks(ai_tutor_source)

      raise "No content found to train" if content_chunks.empty?

      vector_ids = upload_to_pinecone(ai_tutor_source, content_chunks)

      # Save vector IDs and complete training
      ai_tutor_source.update!(vector_ids: vector_ids)
      ai_tutor_source.complete_training!
      ai_tutor_agent.complete_training! unless ai_tutor_agent.any_sources_training?
    rescue => e
      Rails.logger.error "AI training failed for source #{ai_tutor_source_id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      ai_tutor_source.fail_training!(e.message)

      # Check if this was the last training job and update agent accordingly
      unless ai_tutor_agent.any_sources_training?
        ai_tutor_agent.fail_training!("Training failed for source: #{e.message}")
      end

      raise e
    end
  end

  private

  def extract_content_chunks(ai_tutor_source)
    chunks = []

    case ai_tutor_source.source_type
    when 'text'
      ai_tutor_source.ai_tutor_text_sources.each do |text_source|
        next if text_source.content.blank?

        # Mark text source as training
        text_source.start_training!

        begin
          # Split content into chunks (max 1000 chars each)
          content_chunks = split_content(text_source.content)
          content_chunks.each_with_index do |chunk, index|
            chunks << {
              id: "#{ai_tutor_source.id}_text_#{text_source.id}_#{index}",
              content: chunk,
              metadata: {
                ai_tutor_agent_id: ai_tutor_source.ai_tutor_agent_id.to_s,
                ai_tutor_source_id: ai_tutor_source.id.to_s,
                ai_tutor_text_source_id: text_source.id.to_s,
                source_type: 'text',
                title: ai_tutor_source.title.to_s,
                chunk_index: index.to_s
              }
            }
          end

          # Mark text source as trained
          text_source.complete_training!

        rescue => e
          # Mark text source as failed
          text_source.fail_training!(e.message)
          raise e
        end
      end
    when 'web'
      ai_tutor_source.ai_tutor_text_sources.each do |text_source|
        next if text_source.content.blank?

        # Mark text source as training
        text_source.start_training!

        begin
          content_chunks = split_content(text_source.content)
          content_chunks.each_with_index do |chunk, index|
            chunks << {
              id: "#{ai_tutor_source.id}_web_#{text_source.id}_#{index}",
              content: "URL: #{text_source.source_url}\n\nContent: #{chunk}",
              metadata: {
                ai_tutor_agent_id: ai_tutor_source.ai_tutor_agent_id.to_s,
                school_id: ai_tutor_source.school_id.to_s,
                ai_tutor_source_id: ai_tutor_source.id.to_s,
                ai_tutor_text_source_id: text_source.id.to_s,
                ai_tutor_web_source_id: text_source.ai_tutor_web_source_id.to_s,
                source_type: 'web',
                title: ai_tutor_source.title.to_s,
                url: text_source.source_url.to_s, # Use source_url instead of title
                crawl_type: text_source.ai_tutor_web_source&.crawl_type,
                chunk_index: index
              }
            }
          end

          # Mark text source as trained
          text_source.complete_training!

        rescue => e
          # Mark text source as failed
          text_source.fail_training!(e.message)
          raise e
        end
      end

    when 'qa'
      qa_source = ai_tutor_source.ai_tutor_qa_source
      if qa_source&.questions&.any? && qa_source.answer.present?
        qa_source.questions.each_with_index do |question, index|
          chunks << {
            id: "#{ai_tutor_source.id}_qa_#{index}",
            content: "Q: #{question}\nA: #{qa_source.answer}",
            metadata: {
              ai_tutor_agent_id: ai_tutor_source.ai_tutor_agent_id.to_s,
              school_id: ai_tutor_source.school_id.to_s,
              ai_tutor_source_id: ai_tutor_source.id.to_s,
              source_type: 'qa',
              title: ai_tutor_source.title.to_s,
              question: question,
              answer: qa_source.answer
            }
          }
        end
      end

    when 'file'
      file_source = ai_tutor_source.ai_tutor_file_source
      if file_source&.completed? && file_source.content.present?
        # Split file content into chunks
        content_chunks = split_content(file_source.content)
        content_chunks.each_with_index do |chunk, index|
          chunks << {
            id: "#{ai_tutor_source.id}_file_#{index}",
            content: "File: #{file_source.filename}\n\nContent: #{chunk}",
            metadata: {
              ai_tutor_agent_id: ai_tutor_source.ai_tutor_agent_id.to_s,
              school_id: ai_tutor_source.school_id.to_s,
              ai_tutor_source_id: ai_tutor_source.id.to_s,
              source_type: 'file',
              title: ai_tutor_source.title.to_s,
              filename: file_source.filename.to_s,
              file_type: file_source.file_type.to_s,
              chunk_index: index
            }
          }
        end
      end
    end

    chunks
  end

  def split_content(content, max_chunk_size = 1000)
    return [content] if content.length <= max_chunk_size

    chunks = []
    sentences = content.split(/[.!?]+/)
    current_chunk = ""

    sentences.each do |sentence|
      sentence = sentence.strip
      next if sentence.empty?

      if (current_chunk + sentence).length > max_chunk_size
        chunks << current_chunk.strip if current_chunk.present?
        current_chunk = sentence
      else
        current_chunk += (current_chunk.empty? ? "" : ". ") + sentence
      end
    end

    chunks << current_chunk.strip if current_chunk.present?
    chunks
  end

  def upload_to_pinecone(ai_tutor_source, content_chunks)
    return true if content_chunks.empty?

    pinecone_service = PineconeService.for_agent(ai_tutor_source.ai_tutor_agent)

    unless pinecone_service.configured?
      Rails.logger.warn "Pinecone not configured, skipping vector upload"
      return false
    end

    Rails.logger.info "Uploading #{content_chunks.length} chunks to Pinecone for source #{ai_tutor_source.id}"

    begin
      config = RubyLLM::Configuration.new
      config.openai_api_key = ENV['OPENAI_ACCESS_TOKEN']
      context = RubyLLM::Context.new(config)

      vector_ids = []

      content_chunks.each_with_index do |chunk, index_num|
        embedding = RubyLLM.embed(chunk[:content], context: context)
        vector_id = "source_#{ai_tutor_source.id}_chunk_#{index_num}_#{SecureRandom.hex(4)}"

        metadata = chunk[:metadata].merge({
          chunk_index: index_num,
          text: chunk[:content],
          source_name: ai_tutor_source.title,
          agent_id: ai_tutor_source.ai_tutor_agent_id.to_s
        })
        pinecone_service.index.upsert(
          vectors: [{
            id: vector_id,
            values: embedding.vectors,
            metadata: metadata
          }],
          namespace: pinecone_service.namespace
        )

        vector_ids << vector_id
        Rails.logger.debug "Embedded chunk #{index_num} for source #{ai_tutor_source.id}"
      end

      Rails.logger.info "Successfully uploaded #{vector_ids.length} vectors to Pinecone"
      vector_ids
    rescue => e
      Rails.logger.error "Error uploading to Pinecone: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      []
    end
  end
end
