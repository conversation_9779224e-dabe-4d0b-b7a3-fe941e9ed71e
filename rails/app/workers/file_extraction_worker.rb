class FileExtractionWorker
  include Sidekiq::Worker
  sidekiq_options queue: :file_extraction, retry: 3

  def perform(file_source_id)
    file_source = AiTutorFileSource.find(file_source_id)

    Rails.logger.info "Starting file extraction for #{file_source_id}: #{file_source.filename}"

    begin
      # Start extraction
      file_source.start_extraction!

      # Extract content based on file type
      extracted_content = extract_content(file_source)

      if extracted_content.present?
        # For large content (>50KB), split into multiple text sources
        if extracted_content.length > 50_000
          create_chunked_text_sources(file_source, extracted_content)
          file_source.complete_extraction!("Content split into #{(extracted_content.length / 50_000.0).ceil} chunks")
        else
          file_source.complete_extraction!(extracted_content)
        end
        Rails.logger.info "Completed file extraction for #{file_source_id}: #{extracted_content.length} characters"
      else
        file_source.fail_extraction!('No content could be extracted from the file')
        Rails.logger.warn "No content extracted from file #{file_source_id}"
      end

    rescue => e
      Rails.logger.error "File extraction failed for #{file_source_id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      file_source.fail_extraction!(e.message)
      raise e
    end
  end

  private

  def extract_content(file_source)
    file_path = file_source.file_path.path
    return nil unless File.exist?(file_path)

    case file_source.file_type
    when 'text/plain', 'text/csv'
      extract_text_file(file_path)
    when 'application/pdf'
      extract_pdf_content(file_path)
    when 'application/msword'
      extract_doc_content(file_path)
    when 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      extract_docx_content(file_path)
    when 'application/vnd.ms-excel'
      extract_xls_content(file_path)
    when 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      extract_xlsx_content(file_path)
    when 'application/vnd.ms-powerpoint'
      extract_ppt_content(file_path)
    when 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
      extract_pptx_content(file_path)
    else
      raise "Unsupported file type: #{file_source.file_type}"
    end
  end

  def extract_text_file(file_path)
    File.read(file_path, encoding: 'UTF-8')
  rescue Encoding::InvalidByteSequenceError
    # Try with different encoding
    File.read(file_path, encoding: 'ISO-8859-1').encode('UTF-8')
  end

  def extract_pdf_content(file_path)
    # Using pdf-reader gem (add to Gemfile if not present)
    begin
      require 'pdf-reader'
      reader = PDF::Reader.new(file_path)
      content = []

      reader.pages.each do |page|
        content << page.text
      end

      content.join("\n\n")
    rescue LoadError
      # Fallback: try using system pdftotext if available
      extract_pdf_with_system_tool(file_path)
    end
  end

  def extract_pdf_with_system_tool(file_path)
    # Try using pdftotext command line tool
    output_file = "#{file_path}.txt"
    system("pdftotext '#{file_path}' '#{output_file}'")

    if File.exist?(output_file)
      content = File.read(output_file)
      File.delete(output_file) # Clean up
      content
    else
      raise "Could not extract PDF content using system tools"
    end
  end

  def extract_docx_content(file_path)
    # Using docx gem (add to Gemfile if not present)
    begin
      require 'docx'
      doc = Docx::Document.open(file_path)
      doc.paragraphs.map(&:text).join("\n")
    rescue LoadError
      # Fallback: try extracting as zip and reading document.xml
      extract_docx_as_zip(file_path)
    end
  end

  def extract_docx_as_zip(file_path)
    require 'zip'
    content = []

    Zip::File.open(file_path) do |zip_file|
      document_xml = zip_file.find_entry('word/document.xml')
      if document_xml
        xml_content = document_xml.get_input_stream.read
        # Simple XML parsing to extract text
        xml_content.scan(/<w:t[^>]*>([^<]*)<\/w:t>/).flatten.join(' ')
      end
    end
  end

  def extract_doc_content(file_path)
    # For .doc files, we might need external tools or gems
    # This is a placeholder - you might want to use antiword or similar
    raise "DOC file extraction not implemented yet. Please convert to DOCX format."
  end

  def extract_xlsx_content(file_path)
    # Using roo gem (add to Gemfile if not present)
    begin
      require 'roo'
      spreadsheet = Roo::Spreadsheet.open(file_path)
      content = []

      spreadsheet.sheets.each do |sheet_name|
        spreadsheet.sheet(sheet_name)
        content << "Sheet: #{sheet_name}"

        (1..spreadsheet.last_row).each do |row|
          row_data = (1..spreadsheet.last_column).map do |col|
            spreadsheet.cell(row, col)
          end.compact.join("\t")
          content << row_data if row_data.present?
        end
        content << ""
      end

      content.join("\n")
    rescue LoadError
      raise "XLSX extraction requires 'roo' gem. Please add it to your Gemfile."
    end
  end

  def extract_xls_content(file_path)
    # Similar to XLSX but for older Excel format
    begin
      require 'roo'
      spreadsheet = Roo::Excel.new(file_path)
      content = []

      spreadsheet.sheets.each do |sheet_name|
        spreadsheet.sheet(sheet_name)
        content << "Sheet: #{sheet_name}"

        (1..spreadsheet.last_row).each do |row|
          row_data = (1..spreadsheet.last_column).map do |col|
            spreadsheet.cell(row, col)
          end.compact.join("\t")
          content << row_data if row_data.present?
        end
        content << ""
      end

      content.join("\n")
    rescue LoadError
      raise "XLS extraction requires 'roo' gem. Please add it to your Gemfile."
    end
  end

  def extract_pptx_content(file_path)
    # PowerPoint extraction is more complex, this is a basic implementation
    require 'zip'
    content = []

    Zip::File.open(file_path) do |zip_file|
      # Look for slide content
      zip_file.entries.each do |entry|
        if entry.name.match?(/ppt\/slides\/slide\d+\.xml/)
          xml_content = entry.get_input_stream.read
          # Extract text from slides
          slide_text = xml_content.scan(/<a:t[^>]*>([^<]*)<\/a:t>/).flatten.join(' ')
          content << slide_text if slide_text.present?
        end
      end
    end

    content.join("\n\n")
  end

  def extract_ppt_content(file_path)
    # For .ppt files, we might need external tools
    raise "PPT file extraction not implemented yet. Please convert to PPTX format."
  end

  def create_chunked_text_sources(file_source, content)
    # Split content into chunks of 50KB each
    chunk_size = 50_000
    chunks = []

    # Split by sentences first to maintain readability
    sentences = content.split(/[.!?]+/)
    current_chunk = ""

    sentences.each do |sentence|
      sentence = sentence.strip
      next if sentence.empty?

      if (current_chunk + sentence).length > chunk_size
        chunks << current_chunk.strip if current_chunk.present?
        current_chunk = sentence
      else
        current_chunk += (current_chunk.empty? ? "" : ". ") + sentence
      end
    end

    chunks << current_chunk.strip if current_chunk.present?

    # Create text sources for each chunk
    chunks.each_with_index do |chunk, index|
      AiTutorTextSource.create!(
        ai_tutor_source: file_source.ai_tutor_source,
        content: chunk,
        format: 'plain',
        character_count: chunk.length,
        word_count: chunk.split.length
      )
    end

    Rails.logger.info "Created #{chunks.length} text source chunks for file #{file_source.filename}"
  end
end
