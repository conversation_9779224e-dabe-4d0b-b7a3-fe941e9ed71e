class ChangeContentToLongtext < ActiveRecord::Migration[7.0]
  def up
    # Change content columns to longtext to support larger files
    change_column :ai_tutor_text_sources, :content, :text, limit: 4294967295
    change_column :ai_tutor_file_sources, :content, :text, limit: 4294967295
  end

  def down
    # Revert back to regular text (this might cause data loss if content is too large)
    change_column :ai_tutor_text_sources, :content, :text
    change_column :ai_tutor_file_sources, :content, :text
  end
end
